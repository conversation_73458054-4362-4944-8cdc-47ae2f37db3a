# frozen_string_literal: true

require 'fileutils'

# Manual version of the realty game starter that avoids any automated scraping.
# It will prompt you to paste raw HTML into a predetermined file in this project
# and will then process it using the appropriate pasarela. The first listing
# initializes the game; the rest are added to the created game.

ActiveRecord::Base.establish_connection(
  YAML.safe_load(File.read('config/database.yml'), aliases: true)['development']['primary']
)

namespace :h2c do
  desc 'Start realty_game from url (manual HTML via file; no Playwright)'
  task start_realty_game_from_url_generic_v4_manual: :environment do
    ENV['RAILS_MASTER_KEY'] = ''
    logger = Logger.new('log/realty_game_task.log')
    logger.level = Logger::DEBUG

    begin
      ActsAsTenant.current_tenant = AgencyTenant.unique_tenant
      logger.info "Set current tenant: #{ActsAsTenant.current_tenant&.id}"

      input_file = 'db/realty_punts/latest.json'
      logger.info "Reading input file: #{input_file}"

      unless File.exist?(input_file)
        logger.error "Input file not found: #{input_file}"
        puts "Input file not found: #{input_file}"
        exit 1
      end

      input_data = JSON.parse(File.read(input_file))
      input_data['api_prefix'] ||= 'https://be-medo.propertywebbuilder.com/api_mgmt/v4'

      property_urls = input_data['property_urls'] || []
      if property_urls.empty?
        logger.error 'No property URLs found in input file'
        puts 'No property URLs found in input file'
        exit 1
      end

      # Handle both old format (array of strings) and new format (array of objects)
      first_property = property_urls.first
      first_url = first_property.is_a?(Hash) ? first_property['url'] : first_property

      logger.info "First property URL: #{first_url}"

      # Log contextual descriptions if available
      property_urls.each_with_index do |property, index|
        if property.is_a?(Hash)
          url = property['url']
          description = property['contextual_description']
          logger.info "Property #{index + 1}: #{url} - #{description}" if description
        else
          logger.info "Property #{index + 1}: #{property}"
        end
      end

      # Portal detection
      def detect_portal_from_url(url)
        case url
        when /buenavistahomes\.eu/
          'buenavista'
        when /onthemarket\.com/
          'onthemarket'
        when /idealista\.com/
          'idealista'
        when /zoopla\.co\.uk/
          'zoopla'
        when /rightmove\.co\.uk/
          'rightmove'
        when /purplebricks\.co\.uk/
          'purplebricks'
        else
          'unknown'
        end
      end

      portal = detect_portal_from_url(first_url)
      logger.info "Detected portal: #{portal}"

      # Helper: read HTML from a predetermined file path
      def read_html_from_file_for(url, html_path)
        puts "\n================ MANUAL HTML INPUT REQUIRED ================"
        puts 'Please paste the FULL HTML into the file below and save it:'
        puts html_path
        puts 'Then return to this terminal and press Enter to continue.'
        puts 'Requested URL:'
        puts url
        puts '-----------------------------------------------------------'

        $stdin.gets

        unless File.exist?(html_path)
          raise "HTML file not found at: #{html_path}"
        end
        html = File.read(html_path)
        if html.to_s.strip.empty?
          raise 'HTML file is empty. Please paste the FULL HTML and save again.'
        end
        puts "[INFO] Read HTML length: #{html.length}"
        html
      end

      # Helper: create or find RSI, attach pasted HTML, and run pasarela
      def build_scraped_item_from_html(url, html, portal, logger)
        rsi = RealtyScrapedItem.find_or_create_for_hpg(url)
        rsi.update!(web_scraper_name: 'ManualPaste', scraped_content_column_name: 'full_content_before_js')
        rsi.update_column('full_content_before_js', html.to_s)

        p_config = RealtyScrapedItem.portal_config[portal]
        unless p_config
          msg = "Unknown portal config for '#{portal}'"
          logger.error msg
          raise msg
        end

        pasarela_class = p_config[:pasarela].constantize
        pasarela_class.new(rsi).call

        # Task-level minimal fallback if pasarela produced empty data
        if rsi.extracted_asset_data.blank? || rsi.extracted_listing_data.blank?
          logger.warn 'Pasarela produced empty data; attempting minimal extraction from HTML...'
          begin
            doc = Nokogiri::HTML(html)
            title = doc.at('h1')&.text&.strip
            # rough numeric extraction for price
            price_match = doc.text.match(/[€\$]?\s*([\d\.\,]{4,})/)
            price_num = nil
            if price_match
              digits = price_match[1].gsub(',', '').gsub('.', '')
              price_num = digits.to_i
            end
            cents = price_num ? (price_num * 1) : nil

            minimal_asset = { 'title' => title }.compact
            minimal_listing = { 'title' => title }
            if cents
              minimal_listing['price_sale_current_cents'] = cents
              minimal_listing['price_sale_current_currency'] = 'EUR'
            end

            rsi.update(
              extracted_asset_data: minimal_asset.presence || { 'title' => title },
              extracted_listing_data: minimal_listing.presence || { 'title' => title },
              extracted_image_urls: rsi.extracted_image_urls || []
            )
          rescue StandardError => e
            logger.error "Minimal extraction failed: #{e.message}"
          end
        end

        if rsi.extracted_asset_data.blank? || rsi.extracted_listing_data.blank?
          msg = 'Extracted asset data or listing data is empty after pasarela and minimal fallback. Please verify the pasted HTML.'
          logger.error msg
          raise msg
        end

        rsi
      end

      # Helper: POST utilities
      def http_json_post(url, body, logger)
        response = HTTParty.post(
          url,
          body: body.to_json,
          headers: { 'Content-Type' => 'application/json', 'Accept' => 'application/json' }
        )
        unless response.success?
          logger.error("POST #{url} failed: #{response.code} #{response.body}")
          raise "Failed POST to #{url}: #{response.body}"
        end
        response
      end

      # Prepare manual HTML file path
      html_dir = File.join(Dir.pwd, 'manual_html')
      FileUtils.mkdir_p(html_dir)
      html_file_path = File.join(html_dir, 'listing.html')

      # FIRST LISTING: initialize game
      puts "Using manual HTML file: #{html_file_path}"
      first_html = read_html_from_file_for(first_url, html_file_path)
      first_rsi = build_scraped_item_from_html(first_url, first_html, portal, logger)

      init_url = "#{input_data['api_prefix']}/realty_games_mgmt/init_game_with_prepped_listing_hash"
      init_body = {
        realty_game_slug: input_data['realty_game_slug'] || 'regular-game',
        is_one_off_game: input_data['is_one_off_game'],
        one_off_mgmt_code: input_data['one_off_mgmt_code'],
        vendor_name: input_data['vendor_name'] || 'vendor_missing',
        game_title: input_data['game_title'],
        game_description: input_data['game_description'],
        game_bg_image_url: input_data['background_image'],
        scoot_subdomain: input_data['scoot_subdomain'],
        retrieval_portal: portal,
        retrieval_end_point: first_url,
        extracted_asset_data: first_rsi.extracted_asset_data,
        extracted_listing_data: first_rsi.extracted_listing_data,
        extracted_image_urls: first_rsi.extracted_image_urls
      }

      logger.info 'Initializing game with manual prepped listing hash'
      init_response = http_json_post(init_url, init_body, logger)
      realty_game_id = JSON.parse(init_response.body)['realty_game_id']
      logger.info "Created realty game ID: #{realty_game_id}"
      puts "Game initialized. Realty game ID: #{realty_game_id}"

      # REMAINING LISTINGS: add to game one by one
      remaining = property_urls.drop(1)
      remaining.each_with_index do |property, index|
        url = property.is_a?(Hash) ? property['url'] : property
        logger.info "Processing additional listing #{index + 2}/#{property_urls.size}: #{url}"

        puts "Using manual HTML file: #{html_file_path}"
        html = read_html_from_file_for(url, html_file_path)
        rsi = build_scraped_item_from_html(url, html, portal, logger)

        add_url = "#{input_data['api_prefix']}/realty_games_mgmt/add_prepped_listing_to_game"
        add_body = {
          realty_game_id: realty_game_id,
          retrieval_portal: portal,
          retrieval_end_point: url,
          extracted_asset_data: rsi.extracted_asset_data,
          extracted_listing_data: rsi.extracted_listing_data,
          extracted_image_urls: rsi.extracted_image_urls
        }

        logger.info 'Adding manual prepped listing to game'
        http_json_post(add_url, add_body, logger)
        puts "Added listing: #{url}"
      end

      logger.info "All manual listings processed. Realty game ID: #{realty_game_id}"
      puts "All manual listings processed. Realty game ID: #{realty_game_id}"
    rescue StandardError => e
      logger.error "Error: #{e.message}"
      logger.error e.backtrace.join("\n")
      puts 'An error occurred. Check log/realty_game_task.log for details.'
      raise
    end
  end
end
