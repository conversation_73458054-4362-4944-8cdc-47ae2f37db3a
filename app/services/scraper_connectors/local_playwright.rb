require 'playwright'
# above from this gem:
# https://github.com/YusukeIwaki/playwright-ruby-client
require 'fileutils'
require 'securerandom'

module ScraperConnectors
  class LocalPlaywright
    # was often timing out at 9000ms so increased to 19000ms
    DEFAULT_PLAYWRIGHT_TIMEOUT = 39_000
    DEFAULT_VIEWPORT = { width: 1280, height: 720 }.freeze
    DEFAULT_LOCALE = 'en-GB'.freeze
    BROWSER_ARGS = ['--disable-blink-features=AutomationControlled'].freeze
    
    # Enhanced browser arguments for better stealth mode (optional)
    STEALTH_BROWSER_ARGS = [
      '--disable-blink-features=AutomationControlled',
      '--no-first-run',
      '--no-service-autorun',
      '--password-store=basic',
      '--use-mock-keychain',
      '--disable-background-networking',
      '--disable-extensions',
      '--disable-default-apps',
      '--disable-sync',
      '--disable-web-security',
      '--no-default-browser-check',
      '--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    ].freeze
    
    BASE_USER_DATA_DIR = File.join(Dir.pwd, 'playwright_user_data').freeze

    # Class method to forcefully clean up all user data directories
    # Use this if you encounter persistent lock issues
    def self.force_cleanup_all_user_data
      if Dir.exist?(BASE_USER_DATA_DIR)
        puts "[INFO] Force cleaning all user data directories in #{BASE_USER_DATA_DIR}"
        begin
          FileUtils.rm_rf(BASE_USER_DATA_DIR)
          puts '[INFO] All user data directories cleaned successfully'
        rescue StandardError => e
          puts "[ERROR] Failed to force clean user data directories: #{e.message}"
        end
      else
        puts '[INFO] No user data directory found to clean'
      end
    end

    # 4 july 2025 - I'm not convinced about the advantage
    # creating browser instances etc simply on initialisation...
    def initialize(_scrape_instance = nil, headless: true, stealth_mode: false, pre_extraction_wait_ms: 0, wait_for_enter_before_extraction: false)
      puts '[INFO] Initializing LocalPlaywright...'

      # Store preferences
      @headless = headless
      @stealth_mode = stealth_mode
      @pre_extraction_wait_ms = pre_extraction_wait_ms.to_i
      @wait_for_enter_before_extraction = !!wait_for_enter_before_extraction

      # Create a unique user data directory to avoid conflicts
      @user_data_dir = create_unique_user_data_dir
      puts "[INFO] Using unique user data directory: #{@user_data_dir}"
      puts "[INFO] Stealth mode: #{@stealth_mode ? 'enabled' : 'disabled'}"

      # Initialize instance variables that will be set in setup_browser
      @playwright_execution = nil # Manages the driver process
      @playwright_api = nil      # The API entrypoint (has .chromium, .firefox, etc.)
      @browser = nil             # The Browser or BrowserContext (for persistent context)
      @context = nil             # The BrowserContext

      puts '[INFO] Setting up browser...'
      setup_browser
      if @context
        puts '[INFO] LocalPlaywright initialized successfully.'
      else
        puts '[ERROR] LocalPlaywright initialization failed due to browser setup error.'
        raise StandardError, 'Failed to initialize LocalPlaywright: Browser context not set up.'
      end
    end

    def retrieve_data_from_connector(
      incoming_uri, include_trailing_slash: false, is_search_scrape: false
    )
      puts "include_trailing_slash: #{include_trailing_slash}"
      puts "[INFO] Starting retrieve_data_from_connector for URI: #{incoming_uri}, is_search_scrape: #{is_search_scrape}"
      uri = URI.parse(incoming_uri.to_s)
      puts "[INFO] Parsed URI: #{uri}"

      unless @context
        puts '[ERROR] Browser context is not initialized. Aborting retrieve_data_from_connector.'
        return handle_error(StandardError.new('Browser context not initialized. Setup may have failed.'))
      end

      puts "[INFO] Fetching content for URI: #{uri}..."
      content = fetch_content(uri, is_search_scrape: is_search_scrape)
      target_field = if content.is_a?(Hash)
                       'script_json'
                     else
                       'full_content_before_js'
                     end

      response = {
        returned_content: content,
        scrape_item_target: target_field
      }
      puts "[INFO] retrieve_data_from_connector finished for URI: #{incoming_uri}. Response keys: #{response.keys}"
      response
    rescue StandardError => e
      puts "[ERROR] Error in retrieve_data_from_connector: #{e.class} - #{e.message}"
      puts "[ERROR] Backtrace: #{e.backtrace.join("\n")}"
      puts '[INFO] Calling handle_error...'
      handle_error(e)
    end

    def cleanup
      puts '[INFO] Starting cleanup...'
      if @context
        puts '[INFO] Closing browser context/browser...'
        begin
          @context.close
          puts '[INFO] Browser context/browser closed.'
        rescue Playwright::Error => e
          puts "[WARN] Error closing context: #{e.class} - #{e.message}"
        end
        @context = nil
        @browser = nil
      else
        puts '[WARN] No context to close.'
      end

      @playwright_api = nil

      if @playwright_execution
        puts '[INFO] Stopping Playwright execution context (driver)...'
        begin
          @playwright_execution.stop
          puts '[INFO] Playwright execution context stopped.'
        rescue Playwright::Error => e
          puts "[WARN] Error stopping Playwright execution context: #{e.class} - #{e.message}"
        end
        @playwright_execution = nil
      else
        puts '[WARN] No Playwright execution context to stop.'
      end

      # Clean up the unique user data directory
      cleanup_user_data_dir

      puts '[INFO] Cleanup finished.'
    end

    private

    def create_unique_user_data_dir
      # Create base directory if it doesn't exist
      FileUtils.mkdir_p(BASE_USER_DATA_DIR)

      # Create a unique subdirectory with timestamp and random component
      timestamp = Time.now.strftime('%Y%m%d_%H%M%S')
      random_id = SecureRandom.hex(4)
      unique_dir = File.join(BASE_USER_DATA_DIR, "session_#{timestamp}_#{random_id}")

      # Clean up old directories (keep only last 5 sessions to prevent disk bloat)
      cleanup_old_user_data_dirs

      FileUtils.mkdir_p(unique_dir)
      unique_dir
    end

    def cleanup_old_user_data_dirs
      return unless Dir.exist?(BASE_USER_DATA_DIR)

      # Get all session directories sorted by creation time (newest first)
      session_dirs = Dir.glob(File.join(BASE_USER_DATA_DIR, 'session_*'))
                        .select { |path| File.directory?(path) }
                        .sort_by { |path| File.ctime(path) }
                        .reverse

      # Keep only the 5 most recent sessions, remove the rest
      dirs_to_remove = session_dirs[5..-1] || []
      dirs_to_remove.each do |dir|
        puts "[INFO] Removing old user data directory: #{dir}"
        FileUtils.rm_rf(dir)
      rescue StandardError => e
        puts "[WARN] Failed to remove old user data directory #{dir}: #{e.message}"
      end
    end

    def cleanup_user_data_dir
      return unless @user_data_dir && Dir.exist?(@user_data_dir)

      begin
        puts "[INFO] Cleaning up user data directory: #{@user_data_dir}"
        FileUtils.rm_rf(@user_data_dir)
        puts '[INFO] User data directory cleaned up successfully'
      rescue StandardError => e
        puts "[WARN] Failed to clean up user data directory #{@user_data_dir}: #{e.message}"
      end
    end

    def ensure_clean_user_data_dir
      # If directory exists, try to remove any lock files that might prevent startup
      return unless Dir.exist?(@user_data_dir)

      lock_patterns = [
        'SingletonLock',
        'lockfile',
        '*.lock',
        'Default/Preferences'
      ]

      lock_patterns.each do |pattern|
        lock_files = Dir.glob(File.join(@user_data_dir, '**', pattern))
        lock_files.each do |lock_file|
          File.delete(lock_file) if File.exist?(lock_file)
          puts "[INFO] Removed potential lock file: #{lock_file}"
        rescue StandardError => e
          puts "[WARN] Could not remove lock file #{lock_file}: #{e.message}"
        end
      end
    end

    def setup_browser
      puts '[INFO] Starting setup_browser...'

      # Ensure clean user data directory
      ensure_clean_user_data_dir

      cli_path = ENV['PLAYWRIGHT_CLI_PATH'] || './node_modules/.bin/playwright'
      puts "[INFO] Playwright CLI path: #{cli_path}"

      @playwright_execution = Playwright.create(playwright_cli_executable_path: cli_path)
      puts '[INFO] Playwright Execution context created.'

      @playwright_api = @playwright_execution.playwright
      puts '[INFO] Playwright API entrypoint obtained.'

      puts "[INFO] Launching persistent Chromium context with USER_DATA_DIR: #{@user_data_dir}"
      browser_args = @stealth_mode ? STEALTH_BROWSER_ARGS : BROWSER_ARGS
      puts "[INFO] Headless: #{@headless}, Args: #{browser_args}, Viewport: #{DEFAULT_VIEWPORT}, Locale: #{DEFAULT_LOCALE}"

      # Add retry logic for browser startup
      max_retries = 3
      retry_count = 0

      loop do
        launch_options = {
          headless: @headless,
          args: browser_args,
          viewport: DEFAULT_VIEWPORT
        }
        
        # Add extra stealth options if enabled
        if @stealth_mode
          launch_options.merge!(
            locale: DEFAULT_LOCALE
          )
        end
        
        @browser = @playwright_api.chromium.launch_persistent_context(
          @user_data_dir,
          **launch_options
        )
        @context = @browser
        
        # Apply stealth modifications if enabled
        apply_basic_stealth_modifications if @stealth_mode
        
        puts '[INFO] Persistent browser context launched and assigned.'
        break
      rescue StandardError => e
        retry_count += 1
        raise unless retry_count <= max_retries && e.message.include?('SingletonLock')

        puts "[WARN] Browser startup failed (attempt #{retry_count}/#{max_retries}): #{e.message}"
        puts '[INFO] Cleaning user data directory and retrying...'

        # Clean up and recreate the user data directory
        cleanup_user_data_dir
        @user_data_dir = create_unique_user_data_dir

        sleep(1) # Brief pause before retry

        # Re-raise if max retries exceeded or different error
      end

      puts '[INFO] setup_browser finished.'
    rescue StandardError => e
      puts "[ERROR] Failed to setup browser: #{e.class} - #{e.message}"
      puts "[ERROR] Backtrace: #{e.backtrace.join("\n")}"
      @context = nil
      @browser = nil
      @playwright_api = nil
      if @playwright_execution
        puts '[INFO] Attempting to stop Playwright execution context due to setup error...'
        begin
          @playwright_execution.stop
        rescue StandardError
          puts '[WARN] Failed to stop Playwright execution during error handling.'
        end
        @playwright_execution = nil
      end

      # Clean up user data directory on failure
      cleanup_user_data_dir

      raise # Re-raise the error to be handled by initialize
    end

    def apply_basic_stealth_modifications
      return unless @stealth_mode && @context

      puts '[INFO] Applying basic stealth modifications...'
      
      # Note: In Playwright Ruby, we'll apply stealth modifications 
      # directly to pages when they are created rather than using 
      # context-level event listeners
    end

    def apply_stealth_to_page(page)
      # Remove webdriver property
      page.add_init_script(script: <<~JS)
        Object.defineProperty(navigator, 'webdriver', {
          get: () => undefined,
        });
      JS

      # Override plugins to appear more human-like
      page.add_init_script(script: <<~JS)
        Object.defineProperty(navigator, 'plugins', {
          get: () => [1, 2, 3, 4, 5],
        });
      JS

      # Mock Chrome runtime
      page.add_init_script(script: <<~JS)
        window.chrome = {
          runtime: {},
        };
      JS
    end

    def detect_cloudflare_challenge(page)
      return false unless page
      
      begin
        content = page.content
        title = page.title rescue ''
        
        cloudflare_indicators = [
          'Just a moment...',
          'Checking your browser',
          'DDoS protection by Cloudflare',
          'cf-browser-verification'
        ]
        
        cloudflare_indicators.any? do |indicator|
          content.include?(indicator) || title.include?(indicator)
        end
      rescue StandardError => e
        puts "[WARN] Error detecting Cloudflare challenge: #{e.message}"
        false
      end
    end

    def fetch_content(
      uri, is_search_scrape: false, local_playwright_timeout: DEFAULT_PLAYWRIGHT_TIMEOUT
    )
      puts "[INFO] Starting fetch_content for URI: #{uri}, is_search_scrape: #{is_search_scrape}"
      unless @context
        puts '[ERROR] Browser context (@context) is not initialized in fetch_content.'
        raise StandardError, 'Browser context not initialized'
      end

      puts '[INFO] Creating new page in persistent context...'
      page = @context.new_page
      puts '[INFO] New page created.'

      content_result = nil
      begin
        # Apply stealth modifications to this page if enabled
        apply_stealth_to_page(page) if @stealth_mode

        puts "[INFO] Navigating page to: #{uri}"
        page.goto(uri.to_s, timeout: local_playwright_timeout)
        puts "[INFO] Page navigation to #{uri} complete."

        # Quick idealista/datadome handling
        if uri.host =~ /idealista\.com/
          # If DataDome title or captcha iframe detected, wait and try reload once
          title = (page.title rescue '')
          body_html = (page.content rescue '')
          if title.include?('idealista.com') && body_html.include?('captcha-delivery.com')
            puts '[INFO] DataDome captcha suspected on idealista. Waiting briefly and reloading once.'
            page.wait_for_timeout(2000)
            page.reload
            page.wait_for_timeout(1000)
          end

          # Try to accept cookie consent if present
          begin
            # Common buttons in ES/EN
            consent_selectors = [
              'button#didomi-notice-agree-button',
              'button[aria-label="Aceptar"]',
              'button:has-text("Aceptar")',
              'button:has-text("Accept")',
              'div[data-testid="cookie-banner"] button',
            ]
            consent_selectors.each do |sel|
              btn = page.query_selector(sel)
              if btn
                puts "[INFO] Clicking cookie consent button: #{sel}"
                btn.click rescue nil
                page.wait_for_timeout(500)
                break
              end
            end
          rescue StandardError => e
            puts "[WARN] Cookie consent handling error: #{e.message}"
          end
        end

        # Check for Cloudflare challenge if stealth mode is enabled
        if @stealth_mode && detect_cloudflare_challenge(page)
          puts '[INFO] Cloudflare challenge detected, waiting for completion...'
          page.wait_for_timeout(5000)
          if detect_cloudflare_challenge(page)
            puts '[WARN] Cloudflare challenge still active after waiting'
          else
            puts '[INFO] Cloudflare challenge appears resolved'
          end
        end

        # If running non-headless with a configured pre-extraction wait, give the user time to interact
        if !@headless && @pre_extraction_wait_ms.to_i.positive?
          puts "[INFO] Waiting \\#{@pre_extraction_wait_ms}ms for manual interaction before extraction..."
          page.wait_for_timeout(@pre_extraction_wait_ms)
        end

        puts '[INFO] Sleeping for 1 second to allow JS execution...'
        sleep(1)
        puts '[INFO] Sleep finished.'

        if is_search_scrape
          puts '[INFO] is_search_scrape is true. Fetching page.content.'
          content_result = page.content
        else
          puts "[INFO] is_search_scrape is false. Calling extract_content for host: #{uri.host}"
          content_result = extract_content(page, uri)
        end
        puts "[INFO] Content extraction/fetch complete for #{uri}."
      rescue Playwright::Error => e
        puts "[ERROR] Playwright error during fetch_content for #{uri}: #{e.class} - #{e.message}"
        puts "[ERROR] Playwright error backtrace: #{e.backtrace.join("\n")}"
        raise
      ensure
        puts "[INFO] Ensuring page is closed for #{uri}..."
        if page && !page.closed?
          begin
            page.close
            puts "[INFO] Page closed for #{uri}."
          rescue Playwright::Error => e
            puts "[WARN] Error closing page for #{uri}: #{e.class} - #{e.message}"
          end
        elsif page && page.closed?
          puts "[INFO] Page was already closed for #{uri}."
        else
          puts "[INFO] Page object was nil (not created successfully) for #{uri}."
        end
      end
      puts "[INFO] fetch_content finished for URI: #{uri}. Returning content."
      content_result
    end

    def extract_content(page, uri)
      puts "[INFO] Starting extract_content for URI host: #{uri.host}"
      content = case uri.host
                when /rightmove\.co\.uk/
                  puts '[INFO] Host matches rightmove.co.uk. Evaluating window.PAGE_MODEL.'
                  page.evaluate('window.PAGE_MODEL')
                when /onthemarket\.com/
                  puts '[INFO] Host matches onthemarket.com. Evaluating window.__NEXT_DATA__.'
                  page.evaluate('window.__NEXT_DATA__')
                when /idealista\.com/
                  puts '[INFO] Host matches idealista.com. Attempting to extract window.__INITIAL_STATE__ JSON.'
                  begin
                    # Wait briefly for scripts to load (extend wait slightly compared to generic 1s sleep already done before)
                    page.wait_for_timeout(500) # additional small wait just for Idealista
                    json_state = page.evaluate(<<~JS)
                      () => {
                        try {
                          const scripts = Array.from(document.scripts);
                          const target = scripts.find(s => s.textContent && s.textContent.includes('window.__INITIAL_STATE__'));
                          if (!target) { return null; }
                          const match = target.textContent.match(/window.__INITIAL_STATE__\s*=\s*({.*});?/);
                          if (match && match[1]) {
                            try { return JSON.parse(match[1]); } catch(e){ return { _rawState: match[1], _parseError: e.message }; }
                          }
                          return null;
                        } catch(e) { return { _idealistaExtractionError: e.message }; }
                      }
                    JS
                    if json_state.is_a?(Hash) && (json_state.keys - ['_rawState','_parseError']).any?
                      puts '[INFO] Successfully extracted Idealista JSON state.'
                      json_state
                    else
                      puts '[WARN] Idealista JSON state not found or malformed. Falling back to full HTML content.'
                      page.content
                    end
                  rescue StandardError => e
                    puts "[WARN] Idealista JSON extraction raised #{e.class}: #{e.message}. Falling back to full HTML."
                    page.content
                  end
                else
                  puts '[INFO] Host not specifically handled. Returning full page.content.'
                  page.content
                end
      puts "[INFO] extract_content finished for URI host: #{uri.host}."
      content
    end

    def handle_error(error)
      puts "[INFO] Starting handle_error for error: #{error.class} - #{error.message}"
      response = {
        returned_content: nil,
        scrape_item_target: 'full_content_before_js',
        error: "Scraping failed: #{error.class} - #{error.message}"
      }
      puts '[INFO] handle_error finished. Returning error response.'
      response
    end
  end
end

if __FILE__ == $PROGRAM_NAME
  puts '--- Starting LocalPlaywright Test ---'
  connector = nil
  begin
    # Test with stealth mode enabled for Cloudflare handling
    puts '[INFO] Initializing LocalPlaywright with stealth mode enabled...'
    connector = ScraperConnectors::LocalPlaywright.new(headless: false, stealth_mode: true)
    if connector.instance_variable_get(:@context)
      puts "\n--- Test 1: Generic Site (example.com) ---"
      data1 = connector.retrieve_data_from_connector('http://example.com')
      if data1[:error]
        puts "Error fetching example.com: #{data1[:error]}"
      else
        puts "Fetched example.com. Content length: #{data1[:returned_content].length if data1[:returned_content]}"
      end

      puts "\n--- Test 2: Another Generic Site (bing.com with search_scrape) ---"
      data2 = connector.retrieve_data_from_connector('https://www.bing.com', is_search_scrape: true)
      if data2[:error]
        puts "Error fetching bing.com (search scrape): #{data2[:error]}"
      else
        puts "Fetched bing.com (search scrape). Content length: #{data2[:returned_content].length if data2[:returned_content]}"
      end

      puts "\n--- Test 3: Site-specific extraction (will fallback for example.com) ---"
      data3 = connector.retrieve_data_from_connector('http://example.com', is_search_scrape: false)
      if data3[:error]
        puts "Error fetching example.com (site-specific fallback): #{data3[:error]}"
      else
        puts "Fetched example.com (site-specific fallback). Content length: #{data3[:returned_content].length if data3[:returned_content]}"
      end

      puts "\n--- Test 4: Error Handling (invalid URL that causes navigation error) ---"
      data4 = connector.retrieve_data_from_connector('http://domain-that-does-not-exist-and-will-timeout-abcdefg.com')
      if data4[:error]
        puts "Successfully caught error for invalid URL: #{data4[:error]}"
      else
        puts "Unexpectedly fetched invalid URL. Content: #{data4[:returned_content]}"
      end
    else
      puts '[FATAL] Connector context not initialized. Skipping tests.'
    end
  rescue StandardError => e
    puts "[FATAL] Unhandled error in test script: #{e.class} - #{e.message}"
    puts e.backtrace.join("\n")
  ensure
    puts "\n--- Test script ensure block: Cleaning up ---"
    connector&.cleanup
    puts '--- LocalPlaywright Test Finished ---'
  end
end
