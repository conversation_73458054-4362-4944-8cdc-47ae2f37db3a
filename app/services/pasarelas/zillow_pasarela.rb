# frozen_string_literal: true

module Pasarelas
  class ZillowPasarela
    def initialize(realty_scraped_item)
      @realty_scraped_item = realty_scraped_item
    end

    def call
      # For Zillow, we need to parse HTML content from full_content_before_js
      html_content = @realty_scraped_item.full_content_before_js
      return unless html_content.present? && html_content.length > 100

      begin
        # Parse the HTML to find JSON data or extract directly from HTML
        doc = Nokogiri::HTML(html_content)
        zillow_data = extract_zillow_data(doc)
        return unless zillow_data

        # Map the data to our schema
        listing_data = map_property_to_listing_schema(zillow_data)
        asset_data = map_property_to_asset_schema(zillow_data)

        # Extract image URLs
        extracted_image_urls = extract_image_urls(zillow_data, doc)

        # Save the extracted data directly to the database columns
        @realty_scraped_item.extracted_asset_data = asset_data || {}
        @realty_scraped_item.extracted_listing_data = listing_data || {}
        @realty_scraped_item.extracted_image_urls = extracted_image_urls || []
        
        @realty_scraped_item.save!

        Rails.logger.info("Successfully processed Zillow data for scrape item #{@realty_scraped_item.id}")
      rescue JSON::ParserError => e
        Rails.logger.error("Failed to parse Zillow JSON: #{e.message}")
        return
      rescue StandardError => e
        Rails.logger.error("Error processing Zillow data: #{e.message}")
        return
      end
    end

    private

    def extract_zillow_data(doc)
      # Try to find JSON data in script tags first
      json_data = extract_json_from_scripts(doc)
      
      if json_data
        return json_data
      else
        # Fallback to HTML extraction
        return extract_data_from_html(doc)
      end
    end

    def extract_json_from_scripts(doc)
      # Look for __NEXT_DATA__ script first (common in React apps like Zillow)
      next_data_script = doc.at('script#__NEXT_DATA__')
      if next_data_script
        begin
          return JSON.parse(next_data_script.text)
        rescue JSON::ParserError
          # Continue to other methods
        end
      end

      # Look for JSON in script tags with specific types
      script_tags = doc.css('script[type="application/json"]')
      script_tags.each do |script|
        begin
          json_content = JSON.parse(script.text)
          return json_content if json_content && json_content.is_a?(Hash)
        rescue JSON::ParserError
          next
        end
      end

      # Look for JSON in regular script tags with common patterns
      script_tags = doc.css('script')
      script_tags.each do |script|
        script_text = script.text
        
        # Look for window.__INITIAL_STATE__ or similar patterns
        if script_text.include?('window.__INITIAL_STATE__')
          json_match = script_text.match(/window\.__INITIAL_STATE__\s*=\s*({.*?});/)
          if json_match
            begin
              return JSON.parse(json_match[1])
            rescue JSON::ParserError
              next
            end
          end
        end

        # Look for other common patterns
        if script_text.include?('window.dataLayer') || script_text.include?('window.utag_data')
          json_match = script_text.match(/(window\.dataLayer\s*=\s*\[.*?\]|window\.utag_data\s*=\s*{.*?})/)
          if json_match
            begin
              clean_json = json_match[1].gsub(/^window\.(dataLayer|utag_data)\s*=\s*/, '')
              return JSON.parse(clean_json)
            rescue JSON::ParserError
              next
            end
          end
        end
      end

      nil
    end

    def extract_data_from_html(doc)
      # Fallback HTML extraction
      location_text = extract_location_from_html(doc)
      
      {
        'title' => doc.css('h1').first&.text&.strip,
        'price' => extract_price_from_html(doc),
        'description' => extract_description_from_html(doc),
        'bedrooms' => extract_bedrooms_from_html(doc),
        'bathrooms' => extract_bathrooms_from_html(doc),
        'area' => extract_area_from_html(doc),
        'location' => {
          'address' => location_text,
          'city' => extract_city_from_location_text(location_text),
          'state' => extract_state_from_location_text(location_text),
          'zip_code' => extract_zip_from_location_text(location_text)
        },
        'property_type' => extract_property_type_from_html(doc),
        'features' => extract_features_from_html(doc),
        'year_built' => extract_year_built_from_html(doc),
        'lot_size' => extract_lot_size_from_html(doc)
      }
    end

    def map_property_to_asset_schema(zillow_data)
      # Extract location data
      location_data = zillow_data['location'] || zillow_data.dig('props', 'pageProps', 'property', 'address') || {}
      
      {
        'title' => extract_title_from_data(zillow_data),
        'categories' => extract_categories(zillow_data),
        'city' => extract_city(location_data),
        'city_search_key' => extract_city(location_data)&.downcase&.gsub(/\s+/, '-') || '',
        'constructed_area' => extract_area_value(zillow_data),
        'count_bathrooms' => extract_bathroom_count(zillow_data),
        'count_bedrooms' => extract_bedroom_count(zillow_data),
        'count_garages' => extract_garage_count(zillow_data),
        'count_toilets' => 0, # Not typically available in Zillow data
        'country' => 'US', # Zillow is US-focused
        'description' => extract_description_from_data(zillow_data),
        'details' => extract_property_details(zillow_data),
        'discarded_at' => nil,
        'energy_performance' => extract_energy_performance(zillow_data),
        'energy_rating' => extract_energy_rating(zillow_data),
        'floor' => extract_floor(zillow_data),
        'has_rental_listings' => false,
        'has_sale_listings' => true,
        'has_sold_transactions' => false,
        'host_on_create' => 'zillow.com',
        'is_ai_generated_realty_asset' => false,
        'latitude' => extract_latitude(location_data),
        'longitude' => extract_longitude(location_data),
        'neighborhood' => extract_neighborhood(location_data),
        'plot_area' => extract_plot_area(zillow_data),
        'postal_code' => extract_postal_code(location_data),
        'prop_type' => extract_property_type(zillow_data),
        'prop_type_key' => extract_property_type(zillow_data)&.downcase&.gsub(/\s+/, '-') || '',
        'province' => extract_state(location_data),
        'ra_photos_count' => extract_photo_count(zillow_data),
        'realty_asset_flags' => 0,
        'realty_asset_tags' => extract_tags(zillow_data),
        'reference' => extract_reference(zillow_data),
        'region' => extract_region(location_data),
        'rental_listings_count' => 0,
        'sale_listings_count' => 1,
        'sold_transactions_count' => 0,
        'street_address' => extract_street_address(location_data),
        'street_number' => extract_street_number(location_data),
        'year_construction' => extract_year_construction(zillow_data)
      }
    end

    def map_property_to_listing_schema(zillow_data)
      price_data = extract_price_data(zillow_data)
      
      {
        'agency_name' => extract_agency_name(zillow_data),
        'agency_reference' => extract_agency_reference(zillow_data),
        'area_unit' => 'sqft',
        'constructed_area' => extract_area_value(zillow_data),
        'count_bathrooms' => extract_bathroom_count(zillow_data),
        'count_bedrooms' => extract_bedroom_count(zillow_data),
        'count_garages' => extract_garage_count(zillow_data),
        'count_toilets' => 0,
        'description' => extract_description_from_data(zillow_data),
        'discarded_at' => nil,
        'energy_performance' => extract_energy_performance(zillow_data),
        'energy_rating' => extract_energy_rating(zillow_data),
        'floor' => extract_floor(zillow_data),
        'has_virtual_tour' => extract_virtual_tour_status(zillow_data),
        'is_ai_generated_listing' => false,
        'listing_pages_count' => 0,
        'listing_slug' => extract_listing_slug(zillow_data),
        'listing_tags' => extract_listing_tags(zillow_data),
        'main_video_url' => extract_video_url(zillow_data),
        'obscure_map' => false,
        'page_section_listings_count' => 0,
        'position_in_list' => nil,
        'price_sale_current_cents' => price_data[:current_cents],
        'price_sale_current_currency' => price_data[:currency],
        'price_sale_original_cents' => price_data[:original_cents],
        'price_sale_original_currency' => price_data[:currency],
        'property_board_items_count' => 0,
        'property_reference' => extract_reference(zillow_data),
        'sale_listing_flags' => 0,
        'title' => extract_title_from_data(zillow_data),
        'visible' => true,
        'year_construction' => extract_year_construction(zillow_data)
      }
    end

    def extract_image_urls(zillow_data, doc)
      images = []
      
      # Try to get images from JSON data first
      if zillow_data['images'].is_a?(Array)
        images = zillow_data['images']
      elsif zillow_data.dig('props', 'pageProps', 'property', 'photos').is_a?(Array)
        images = zillow_data.dig('props', 'pageProps', 'property', 'photos')
      elsif zillow_data.dig('property', 'photos').is_a?(Array)
        images = zillow_data.dig('property', 'photos')
      else
        # Fallback to HTML extraction
        doc.css('img').each do |img|
          src = img['src'] || img['data-src'] || img['data-original']
          if src && (src.include?('zillow') || src.start_with?('http'))
            images << src
          end
        end
      end
      
      # Clean and validate image URLs
      images.compact.uniq.select { |url| url.is_a?(String) && url.length > 10 }
    end

    # Helper methods for data extraction
    def extract_title_from_data(data)
      data.dig('props', 'pageProps', 'property', 'streetAddress') || 
      data.dig('property', 'address') ||
      data['title']
    end

    def extract_description_from_data(data)
      data.dig('props', 'pageProps', 'property', 'description') || 
      data.dig('property', 'description') ||
      data['description']
    end

    def extract_categories(data)
      property_type = extract_property_type(data)
      property_type ? [property_type] : []
    end

    def extract_city(location_data)
      location_data['city'] || location_data['locality'] || location_data['municipality']
    end

    def extract_area_value(data)
      area = data.dig('props', 'pageProps', 'property', 'livingArea') || 
             data.dig('property', 'livingArea') || 
             data['area']
      area.is_a?(Numeric) ? area.to_f : area.to_f rescue 0.0
    end

    def extract_bathroom_count(data)
      bathrooms = data.dig('props', 'pageProps', 'property', 'bathrooms') || 
                  data.dig('property', 'bathrooms') || 
                  data['bathrooms']
      bathrooms.is_a?(Numeric) ? bathrooms.to_f : bathrooms.to_f rescue 0.0
    end

    def extract_bedroom_count(data)
      bedrooms = data.dig('props', 'pageProps', 'property', 'bedrooms') || 
                 data.dig('property', 'bedrooms') || 
                 data['bedrooms']
      bedrooms.is_a?(Numeric) ? bedrooms.to_i : bedrooms.to_i rescue 0
    end

    def extract_garage_count(data)
      garages = data.dig('props', 'pageProps', 'property', 'parkingSpaces') || 
                data.dig('property', 'parkingSpaces') || 
                data['garages']
      garages.is_a?(Numeric) ? garages.to_i : garages.to_i rescue 0
    end

    def extract_property_details(data)
      details = {}
      
      # Extract various property details
      if data['features'].is_a?(Array)
        data['features'].each_with_index do |feature, index|
          details["feature_#{index}"] = feature
        end
      end
      
      if data['amenities'].is_a?(Hash)
        details.merge!(data['amenities'])
      end
      
      details
    end

    def extract_energy_performance(data)
      data.dig('energy', 'performance') || data.dig('energy_info', 'performance')
    end

    def extract_energy_rating(data)
      data.dig('energy', 'rating') || data.dig('energy_info', 'rating')
    end

    def extract_floor(data)
      data['floor'] || data.dig('property', 'floor')
    end

    def extract_latitude(location_data)
      lat = location_data['latitude'] || location_data['lat'] || location_data.dig('coordinates', 'latitude')
      lat.is_a?(Numeric) ? lat.to_f : lat.to_f rescue nil
    end

    def extract_longitude(location_data)
      lng = location_data['longitude'] || location_data['lng'] || location_data['lon'] || location_data.dig('coordinates', 'longitude')
      lng.is_a?(Numeric) ? lng.to_f : lng.to_f rescue nil
    end

    def extract_neighborhood(location_data)
      location_data['neighborhood'] || location_data['district'] || location_data['area']
    end

    def extract_plot_area(data)
      plot = data.dig('props', 'pageProps', 'property', 'lotSize') || 
             data.dig('property', 'lotSize') || 
             data['lot_size']
      plot.is_a?(Numeric) ? plot.to_f : plot.to_f rescue 0.0
    end

    def extract_postal_code(location_data)
      location_data['postal_code'] || location_data['zip_code'] || location_data['zipcode']
    end

    def extract_property_type(data)
      data.dig('props', 'pageProps', 'property', 'homeType') || 
      data.dig('property', 'homeType') || 
      data['property_type']
    end

    def extract_state(location_data)
      location_data['state'] || location_data['region'] || location_data['province']
    end

    def extract_photo_count(data)
      if data['images'].is_a?(Array)
        data['images'].length
      elsif data.dig('props', 'pageProps', 'property', 'photos').is_a?(Array)
        data.dig('props', 'pageProps', 'property', 'photos').length
      else
        0
      end
    end

    def extract_tags(data)
      tags = []
      tags << data['property_type'] if data['property_type']
      tags += data['features'] if data['features'].is_a?(Array)
      tags.compact.uniq
    end

    def extract_reference(data)
      data.dig('props', 'pageProps', 'property', 'zpid') || 
      data.dig('property', 'zpid') || 
      data['reference'] || 
      data['id']
    end

    def extract_region(location_data)
      location_data['region'] || location_data['state'] || location_data['province']
    end

    def extract_street_address(location_data)
      location_data['address'] || location_data['street_address'] || location_data['streetAddress']
    end

    def extract_street_number(location_data)
      location_data['street_number'] || location_data['number'] || location_data['streetNumber']
    end

    def extract_year_construction(data)
      year = data.dig('props', 'pageProps', 'property', 'yearBuilt') || 
             data.dig('property', 'yearBuilt') || 
             data['year_built']
      year.is_a?(Numeric) ? year.to_i : year.to_i rescue nil
    end

    def extract_price_data(data)
      price = data.dig('props', 'pageProps', 'property', 'price') || 
              data.dig('property', 'price') || 
              data['price']
      
      if price.is_a?(Hash)
        amount = price['amount'] || price['value']
        currency = price['currency'] || 'USD'
      else
        amount = price
        currency = 'USD'
      end
      
      # Convert to cents
      cents = amount.is_a?(Numeric) ? (amount * 100).to_i : amount.to_i * 100 rescue 0
      
      {
        current_cents: cents,
        original_cents: cents,
        currency: currency
      }
    end

    def extract_agency_name(data)
      data.dig('agent', 'name') || data.dig('listing_agent', 'name') || 'Unknown Agency'
    end

    def extract_agency_reference(data)
      data.dig('agent', 'reference') || data.dig('listing_agent', 'reference')
    end

    def extract_virtual_tour_status(data)
      !!(data['virtual_tour'] || data.dig('media', 'virtual_tour'))
    end

    def extract_listing_slug(data)
      reference = extract_reference(data)
      reference ? reference.to_s.downcase.gsub(/[^a-z0-9]/, '-') : nil
    end

    def extract_listing_tags(data)
      extract_tags(data)
    end

    def extract_video_url(data)
      data.dig('media', 'video') || data.dig('videos', 0, 'url')
    end

    # HTML extraction fallback methods
    def extract_price_from_html(doc)
      price_element = doc.css('[data-testid="price"], .price, [class*="price"]').first
      price_text = price_element&.text&.strip
      return nil unless price_text
      
      # Extract numeric value from price text
      price_match = price_text.match(/[\d,]+/)
      price_match ? price_match[0].gsub(',', '').to_f : 0.0
    end

    def extract_description_from_html(doc)
      desc_element = doc.css('[data-testid="description"], .description, [class*="description"]').first
      desc_element&.text&.strip
    end

    def extract_bedrooms_from_html(doc)
      bedroom_text = doc.text
      bedroom_match = bedroom_text.match(/(\d+)\s*(bed|bedroom)/i)
      bedroom_match ? bedroom_match[1].to_i : 0
    end

    def extract_bathrooms_from_html(doc)
      bathroom_text = doc.text
      bathroom_match = bathroom_text.match(/(\d+)\s*(bath|bathroom)/i)
      bathroom_match ? bathroom_match[1].to_i : 0
    end

    def extract_area_from_html(doc)
      area_text = doc.text
      area_match = area_text.match(/(\d+,?\d*)\s*sqft/i)
      area_match ? area_match[1].gsub(',', '').to_i : 0
    end

    def extract_location_from_html(doc)
      location_element = doc.css('h1, [data-testid="address"], .address').first
      location_element&.text&.strip
    end

    def extract_property_type_from_html(doc)
      # Look for property type indicators
      type_text = doc.text
      if type_text.match(/single.family/i)
        'SingleFamily'
      elsif type_text.match(/condo|condominium/i)
        'Condo'
      elsif type_text.match(/townhouse|townhome/i)
        'Townhouse'
      elsif type_text.match(/apartment/i)
        'Apartment'
      else
        'Unknown'
      end
    end

    def extract_features_from_html(doc)
      features = []
      feature_elements = doc.css('.features, [class*="feature"], [data-testid*="feature"]')
      feature_elements.each do |element|
        features << element.text.strip if element.text.present?
      end
      features
    end

    def extract_year_built_from_html(doc)
      year_text = doc.text
      year_match = year_text.match(/built.{0,10}(\d{4})/i)
      year_match ? year_match[1].to_i : nil
    end

    def extract_lot_size_from_html(doc)
      lot_text = doc.text
      lot_match = lot_text.match(/([\d.]+)\s*acre/i)
      lot_match ? lot_match[1].to_f : 0.0
    end

    def extract_city_from_location_text(location_text)
      return nil unless location_text
      
      # Try to extract city from location text like "Chappaqua, NY 10514"
      parts = location_text.split(',').map(&:strip)
      if parts.length >= 2
        # The city is usually the first part before the first comma
        parts.first
      else
        parts.first
      end
    end

    def extract_state_from_location_text(location_text)
      return nil unless location_text
      
      # Try to extract state from location text like "Chappaqua, NY 10514"
      state_match = location_text.match(/,\s*([A-Z]{2})\s*\d{5}/)
      state_match ? state_match[1] : nil
    end

    def extract_zip_from_location_text(location_text)
      return nil unless location_text
      
      # Try to extract ZIP code from location text like "Chappaqua, NY 10514"
      zip_match = location_text.match(/(\d{5})/)
      zip_match ? zip_match[1] : nil
    end
  end
end
